import sys
sys.path.append("..")

from uptech import UpTech
from motion_controller import MotionController

import cv2
import threading
import time
import numpy as np

"""
仿人散打赛项机器人控制代码

传感器配置说明:
1. 灰度传感器(ADC通道):
   - 前方灰度传感器: ADC通道0
   - 后方灰度传感器: ADC通道1

2. 倾角传感器(ADC通道):
   - 倾角传感器: ADC通道2

3. 摄像头: 用于AprilTag检测
   - 敌方机器人前方: AprilTag ID 1
   - 敌方机器人左侧: AprilTag ID 2  
   - 敌方机器人右侧: AprilTag ID 3
   - 敌方机器人后方: AprilTag ID 4

4. 舵机配置:
   - 左胳膊舵机: 4(前后), 5(上下), 6(上下)
   - 右胳膊舵机: 7(前后), 8(上下), 9(上下)
   
舵机运动方向:
- 4号舵机数值增大: 向前转
- 5号舵机数值增大: 向下转  
- 6号舵机数值增大: 向下转
- 7号舵机数值增大: 向后转
- 8号舵机数值增大: 向下转
- 9号舵机数值增大: 向下转
"""

class HumanoidFightingRobot:
    # 灰度传感器阈值设置 (擂台中心约3400，边缘约450)
    GRAY_TARGET_MIN = 2000  # 目标灰度值下限 (中心区域偏外)
    GRAY_TARGET_MAX = 3000  # 目标灰度值上限 (接近中心区域)
    GRAY_EDGE_THRESHOLD = 650  # 边缘检测阈值，低于此值认为接近边缘 (修改为550)
    
    # 倾角传感器阈值设置
    TILT_FORWARD_THRESHOLD = 2200  # 向前倾倒阈值
    TILT_BACKWARD_THRESHOLD = 1800  # 向后倾倒阈值
    TILT_NORMAL_MIN = 1900  # 正常倾角范围下限
    TILT_NORMAL_MAX = 2100  # 正常倾角范围上限
    
    # 速度设置
    NORMAL_SPEED = 320      # 正常巡航速度
    ATTACK_SPEED = 320      # 攻击速度
    TURN_SPEED = 380        # 转向速度
    RETREAT_SPEED = 320     # 后退速度
    
    # 舵机角度设置
    ARM_NEUTRAL_ANGLE = 512   # 胳膊中性位置
    ARM_FORWARD_ANGLE = 700   # 胳膊向前支撑角度
    ARM_BACKWARD_ANGLE = 300  # 胳膊向后支撑角度
    ARM_DOWN_ANGLE = 700      # 胳膊向下角度
    ARM_UP_ANGLE = 300        # 胳膊向上角度
    SERVO_SPEED = 800         # 舵机转动速度
    
    # 时间设置
    TURN_TIME = 0.5          # 90度转向时间
    BALANCE_TIME = 1.0       # 平衡支撑时间
    ATTACK_TIME = 2.0        # 攻击持续时间

    def __init__(self):
        # 离开出发区 - 向前移动进入比赛区域
        print("离开出发区...")
        motion_controller.move_cmd(380, 380)
        time.sleep(1.2)  # 前进1秒离开出发区
        .motion_controller.move_cmd(300, 300)
        time.sleep(0.8)  # 前进1秒离开出发区
        .motion_controller.move_cmd(0, 0)
        time.sleep(0.1)  # 前进1秒离开出发区
        # 读取几次传感器值，清除可能的初始异常值
        """初始化仿人散打机器人"""
        print("初始化仿人散打机器人...")
        
        # 初始化UpTech控制器
        self.uptech = UpTech()
        # 打开ADC和IO通道
        self.uptech.ADC_IO_Open()
        # 初始化CDS舵机控制
        self.uptech.CDS_Open()
        time.sleep(0.1)
        
        # 设置所有胳膊舵机为位置模式
        for servo_id in [4, 5, 6, 7, 8, 9]:
            self.uptech.CDS_SetMode(servo_id, 0)
            time.sleep(0.1)
        
        # 初始化运动控制器
        self.motion_controller = MotionController()
        # 确保电机停止
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 初始化AprilTag检测器 - 使用OpenCV内置检测器
        try:
            # 尝试使用新版本OpenCV的API
            self.detector = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_APRILTAG_36h11)
            self.parameters = cv2.aruco.DetectorParameters()
        except AttributeError:
            # 如果新API不可用，使用旧版本API
            self.detector = cv2.aruco.Dictionary_get(cv2.aruco.DICT_APRILTAG_36h11)
            self.parameters = cv2.aruco.DetectorParameters_create()
        
        # 初始化摄像头线程
        self.camera_thread = threading.Thread(target=self.camera_loop)
        self.camera_activate = True
        self.camera_thread.daemon = True
        self.camera_thread.start()
        
        # 初始化检测结果
        self.detected_tag_id = None
        self.tag_center_x = None
        self.tag_distance = None

        # 添加平衡控制防抖动
        self.last_balance_action_time = 0
        self.balance_cooldown = 2.0  # 平衡动作冷却时间（秒）

        # 摄像头视野参数
        self.camera_width = 640  # 假设摄像头分辨率宽度
        self.camera_center_x = self.camera_width // 2

        # 卡住检测相关变量
        self.back_gray_history = []  # 后方灰度传感器历史值
        self.stuck_detection_window = 3.0  # 检测窗口时间（秒）
        self.stuck_threshold = 18  # 灰度值变化阈值
        self.last_stuck_action_time = 0  # 上次执行卡住处理的时间
        self.stuck_cooldown = 5.0  # 卡住处理冷却时间（秒）

        # 初始化胳膊到中性位置
        self.reset_arms()
        
        print("仿人散打机器人初始化完成")

    def camera_loop(self):
        """摄像头线程，用于AprilTag检测"""
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Failed to open camera")
            return

        while self.camera_activate:
            ret, frame = cap.read()
            if not ret:
                print("Failed to read frame")
                break

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 使用OpenCV内置AprilTag检测器
            corners, ids, _ = cv2.aruco.detectMarkers(gray, self.detector, parameters=self.parameters)

            if ids is not None and len(ids) > 0:
                # 找到最大的标签（距离最近）
                max_area = 0
                best_id = None
                best_center_x = None
                best_distance = None
                
                for i, tag_id in enumerate(ids.flatten()):
                    if tag_id in [1, 2, 3, 4]:  # 只处理敌方机器人的标签
                        corner = corners[i][0]
                        # 计算标签面积（用于估算距离）
                        area = cv2.contourArea(corner)
                        if area > max_area:
                            max_area = area
                            best_id = tag_id
                            # 计算标签中心X坐标
                            best_center_x = int(np.mean(corner[:, 0]))
                            # 估算距离（面积越大距离越近）
                            best_distance = 10000 / (area + 1)  # 简单的距离估算
                
                if best_id is not None:
                    self.detected_tag_id = best_id
                    self.tag_center_x = best_center_x
                    self.tag_distance = best_distance
                    print(f"检测到敌方机器人标签 ID: {best_id}, 中心X: {best_center_x}, 距离: {best_distance:.1f}")
                else:
                    self.detected_tag_id = None
                    self.tag_center_x = None
                    self.tag_distance = None
            else:
                self.detected_tag_id = None
                self.tag_center_x = None
                self.tag_distance = None

            # 绘制检测结果
            if corners is not None and ids is not None:
                cv2.aruco.drawDetectedMarkers(frame, corners, ids)

            cv2.imshow("AprilTag Detection", frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                self.camera_activate = False
                break

        cap.release()
        cv2.destroyAllWindows()

    def reset_arms(self):
        """重置胳膊到默认位置"""
        print("重置胳膊到默认位置")
        # 左胳膊 (4, 5, 6)
        self.uptech.CDS_SetAngle(4,835, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(5, 800, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(6, 50, self.SERVO_SPEED)
        
        # 右胳膊 (7, 8, 9)
        self.uptech.CDS_SetAngle(7, 115, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(8, 800, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(9, 70, self.SERVO_SPEED)
        
        time.sleep(0.5)

    def get_gray_values(self):
        """获取灰度传感器数值"""
        front_gray = self.uptech.ADC_Get_Channel(0)   # 前方灰度传感器
        back_gray = self.uptech.ADC_Get_Channel(1)    # 后方灰度传感器

        # 记录后方灰度传感器历史值用于卡住检测
        current_time = time.time()
        self.back_gray_history.append((current_time, back_gray))

        # 清理超过检测窗口时间的历史数据
        cutoff_time = current_time - self.stuck_detection_window
        self.back_gray_history = [(t, v) for t, v in self.back_gray_history if t >= cutoff_time]

        return front_gray, back_gray

    def get_tilt_value(self):
        """获取倾角传感器数值"""
        tilt_value = self.uptech.ADC_Get_Channel(2)  # 倾角传感器
        return tilt_value

    def check_stuck(self):
        """检测机器人是否卡住"""
        current_time = time.time()

        # 检查是否在冷却期内
        if current_time - self.last_stuck_action_time < self.stuck_cooldown:
            return False

        # 需要至少有3秒的数据才能判断
        if len(self.back_gray_history) < 2:
            return False

        # 检查历史数据是否覆盖了足够的时间窗口
        oldest_time = self.back_gray_history[0][0]
        if current_time - oldest_time < self.stuck_detection_window:
            return False

        # 计算3秒内后方灰度传感器的最大值和最小值
        gray_values = [value for _, value in self.back_gray_history]
        max_gray = max(gray_values)
        min_gray = min(gray_values)
        gray_diff = max_gray - min_gray

        print(f"卡住检测 - 3秒内后方灰度变化: {gray_diff} (阈值: {self.stuck_threshold})")

        # 如果3秒内变化小于阈值，认为机器人卡住了
        if gray_diff <= self.stuck_threshold:
            print("⚠️  检测到机器人可能卡住了！")
            return True

        return False

    def handle_stuck(self):
        """处理机器人卡住的情况"""
        print("🔄 执行卡住处理：后退 -> 左转 -> 前进")
        current_time = time.time()
        self.last_stuck_action_time = current_time

        # 停止当前动作
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.1)

        # 1. 后退一点
        print("1. 后退脱离卡住位置")
        self.motion_controller.move_cmd(-self.RETREAT_SPEED, -self.RETREAT_SPEED)
        time.sleep(0.5)  # 后退0.5秒

        # 2. 左转
        print("2. 左转改变方向")
        self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
        time.sleep(self.TURN_TIME)  # 转向时间

        # 3. 前进
        print("3. 前进继续运动")
        self.motion_controller.move_cmd(self.NORMAL_SPEED, self.NORMAL_SPEED)
        time.sleep(0.8)  # 前进0.8秒

        # 清空历史数据，重新开始检测
        self.back_gray_history = []
        print("✅ 卡住处理完成")

    def check_balance(self):
        """检查机器人平衡状态并进行平衡控制"""
        tilt_value = self.get_tilt_value()
        current_time = time.time()

        print(f"倾角传感器值: {tilt_value}")

        # 检查是否在冷却期内
        if current_time - self.last_balance_action_time < self.balance_cooldown:
            return "cooldown"

        if tilt_value > self.TILT_FORWARD_THRESHOLD:
            # 向前倾倒，胳膊向前支撑
            print("检测到向前倾倒，胳膊向前支撑")
            self.arms_forward_support()
            self.last_balance_action_time = current_time
            return "forward_tilt"
        elif tilt_value < self.TILT_BACKWARD_THRESHOLD:
            # 向后倾倒，胳膊向后支撑
            print("检测到向后倾倒，胳膊向后支撑")
            self.arms_backward_support()
            self.last_balance_action_time = current_time
            return "backward_tilt"
        elif self.TILT_NORMAL_MIN <= tilt_value <= self.TILT_NORMAL_MAX:
            # 正常状态
            return "normal"
        else:
            # 轻微倾斜，保持当前状态
            return "slight_tilt"

    def arms_forward_support(self):
        """胳膊向前支撑"""
        print("执行胳膊向前支撑")
        # 停止所有移动
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.1)

        self.uptech.CDS_SetAngle(5, 700, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(6, 430, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(8, 700, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(9, 471, self.SERVO_SPEED)
        time.sleep(1)
        self.motion_controller.move_cmd(300, 300)
        time.sleep(1.0)
        
    def arms_backward_support(self):
        """胳膊向后支撑"""
        print("执行胳膊向后支撑")
        # 停止所有移动
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 手臂伸展到支撑位置
        self.uptech.CDS_SetAngle(4, 200, 700)
        self.uptech.CDS_SetAngle(7, 800, 700)
        time.sleep(1.0)
        self.uptech.CDS_SetAngle(5, 750, 700)
        self.uptech.CDS_SetAngle(6, 430, 700)
        self.uptech.CDS_SetAngle(8, 750, 700)
        self.uptech.CDS_SetAngle(9, 471, 700)
        time.sleep(0.5)
        self.motion_controller.move_cmd(-300, -300)
        time.sleep(1.0)

    def navigate_by_gray(self):
        """基于灰度传感器的导航控制"""
        front_gray, back_gray = self.get_gray_values()
        avg_gray = (front_gray + back_gray) / 2

        print(f"灰度传感器值 - 前: {front_gray}, 后: {back_gray}, 平均: {avg_gray:.1f}")

        # 添加灰度状态判断信息
        if front_gray < self.GRAY_EDGE_THRESHOLD:
            print(f"⚠️  前方接近边缘 (灰度: {front_gray} < {self.GRAY_EDGE_THRESHOLD})")
        if back_gray < self.GRAY_EDGE_THRESHOLD:
            print(f"⚠️  后方接近边缘 (灰度: {back_gray} < {self.GRAY_EDGE_THRESHOLD})")

        # 检查是否接近边缘（紧急情况）
        if front_gray < self.GRAY_EDGE_THRESHOLD or back_gray < self.GRAY_EDGE_THRESHOLD:
            print("检测到接近边缘，执行避障")
            # 立即停止当前动作
            self.motion_controller.move_cmd(0, 0)
            time.sleep(0.1)

            # 统一的边缘避障策略：先后退一点，再左转，实现围绕擂台边缘巡航
            print("后退一点避开边缘")
            self.motion_controller.move_cmd(-self.RETREAT_SPEED, -self.RETREAT_SPEED)
            time.sleep(0.3)  # 后退0.3秒

            print("左转继续巡航")
            self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
            time.sleep(self.TURN_TIME)
            
            print("前进一点")
            self.motion_controller.move_cmd(self.NORMAL_SPEED, self.NORMAL_SPEED)
            time.sleep(1.2)
            
            return "edge_avoidance"

        # 在目标灰度范围内
        if self.GRAY_TARGET_MIN <= avg_gray <= self.GRAY_TARGET_MAX:
            return "in_target_zone"

        return "normal_navigation"

    def detect_enemy(self):
        """检测敌方机器人"""
        if self.detected_tag_id is not None:
            return {
                'detected': True,
                'tag_id': self.detected_tag_id,
                'center_x': self.tag_center_x,
                'distance': self.tag_distance
            }
        return {'detected': False}

    def attack_enemy(self, enemy_info):
        """攻击敌方机器人"""
        tag_id = enemy_info['tag_id']
        center_x = enemy_info['center_x']
        distance = enemy_info['distance']

        print(f"攻击敌方机器人 - ID: {tag_id}, 位置: {center_x}, 距离: {distance:.1f}")

        # # 首先根据标签在摄像头中的位置进行对准
        # if center_x is not None:
        #     x_offset = center_x - self.camera_center_x
        #     print(f"标签偏移中心: {x_offset} 像素")

        #     # 如果标签不在中心，先调整方向对准
        #     if abs(x_offset) > 50:  # 允许50像素的误差
        #         if x_offset > 0:
        #             # 标签在右侧，右转对准
        #             print("标签在右侧，右转对准")
        #             self.motion_controller.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
        #             time.sleep(0.2)
        #         else:
        #             # 标签在左侧，左转对准
        #             print("标签在左侧，左转对准")
        #             self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
        #             time.sleep(0.2)

        # 根据标签ID判断敌方机器人的朝向并执行攻击策略
        if tag_id in [1, 2, 3, 4]:
            # 敌方机器人正面，直接攻击
            print("识别到敌方机器人，推倒攻击")
            # 左胳膊 (4, 5, 6)
            self.uptech.CDS_SetAngle(4, 835, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(5, 480, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(6, 400, self.SERVO_SPEED)
            # 右胳膊 (7, 8, 9)
            self.uptech.CDS_SetAngle(7, 115, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(8, 480, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(9, 420, self.SERVO_SPEED)
            time.sleep(0.6)
            # 左胳膊 (4, 5, 6)
            self.uptech.CDS_SetAngle(4, 835, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(5, 840, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(6, 30, self.SERVO_SPEED)
            
            # 右胳膊 (7, 8, 9)
            self.uptech.CDS_SetAngle(7, 115, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(8, 840, self.SERVO_SPEED)
            self.uptech.CDS_SetAngle(9, 50, self.SERVO_SPEED)
            time.sleep(0.6)
            self.motion_controller.move_cmd(320, 320)
            time.sleep(0.3)
        # 攻击后停止
        self.motion_controller.move_cmd(0, 0)

    def start(self):
        """主控制循环"""
        print("仿人散打机器人开始运行")
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.2)

        # 初始化胳膊位置
        self.reset_arms()
        time.sleep(0.2)

        while True:
            try:
                # 1. 优先检查平衡状态
                balance_status = self.check_balance()
                if balance_status in ["forward_tilt", "backward_tilt"]:
                    # 如果检测到倾倒，优先处理平衡，然后继续循环
                    print(f"处理平衡状态: {balance_status}")
                    time.sleep(0.2)
                    # 平衡后重置胳膊
                    self.reset_arms()
                    continue
                elif balance_status == "cooldown":
                    # 在冷却期内，跳过平衡检查
                    pass

                # 2. 检查是否卡住
                if self.check_stuck():
                    self.handle_stuck()
                    continue

                # 3. 检测敌方机器人
                enemy_info = self.detect_enemy()
                if enemy_info['detected']:
                    print(f"发现敌方机器人: {enemy_info}")
                    # 攻击敌方机器人
                    self.attack_enemy(enemy_info)
                    # 攻击后短暂停顿
                    time.sleep(0.2)
                    continue

                # 4. 基于灰度传感器导航
                nav_status = self.navigate_by_gray()
                print(f"导航状态: {nav_status}")

                # 5. 如果在目标区域内且没有敌人，进行巡航
                if nav_status == "in_target_zone":
                    print("在目标区域内巡航")
                    self.motion_controller.move_cmd(self.NORMAL_SPEED, self.NORMAL_SPEED)
                    time.sleep(0.2)

                # 短暂延时避免过于频繁的传感器读取
                time.sleep(0.1)

            except Exception as e:
                print(f"主循环异常: {e}")
                # 发生异常时停止运动并重置胳膊
                self.motion_controller.move_cmd(0, 0)
                self.reset_arms()
                time.sleep(0.2)

    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        self.camera_activate = False
        if self.camera_thread.is_alive():
            self.camera_thread.join(timeout=2)
        self.motion_controller.move_cmd(0, 0)
        self.reset_arms()
        cv2.destroyAllWindows()

def main():
    """主函数"""
    robot = HumanoidFightingRobot()
    print("仿人散打机器人初始化完成")

    # 等待系统稳定
    time.sleep(0.2)
    # # 离开出发区 - 向前移动进入比赛区域
    # print("离开出发区...")
    # robot.motion_controller.move_cmd(380, 380)
    # time.sleep(1.2)  # 前进1秒离开出发区
    # robot.motion_controller.move_cmd(300, 300)
    # time.sleep(0.8)  # 前进1秒离开出发区
    # robot.motion_controller.move_cmd(0, 0)
    # time.sleep(0.1)  # 前进1秒离开出发区
    # # 读取几次传感器值，清除可能的初始异常值
    for _ in range(5):
        robot.get_gray_values()
        robot.get_tilt_value()
        time.sleep(0.1)

    print("机器人准备开始运行")

    # # 离开出发区 - 向前移动进入比赛区域
    # print("离开出发区...")
    # robot.motion_controller.move_cmd(380, 380)
    # time.sleep(1.2)  # 前进1秒离开出发区
    # robot.motion_controller.move_cmd(300, 300)
    # time.sleep(0.8)  # 前进1秒离开出发区
    # robot.motion_controller.move_cmd(0, 0)
    # time.sleep(0.1)  # 前进1秒离开出发区
    

    try:
        # 开始主控制循环
        robot.start()
    except KeyboardInterrupt:
        print("程序被用户中断")
    finally:
        robot.cleanup()

if __name__ == "__main__":
    main()

